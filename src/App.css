.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app h1 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
  font-size: 2.5rem;
}

.form-container {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-container h2 {
  color: #495057;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.file-input {
  padding: 0.5rem !important;
  background-color: white;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.btn-primary {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-header {
  background: #007bff;
  color: white;
  margin: 0;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h2 {
  margin: 0;
  font-size: 1.25rem;
}

.btn-print-all {
  background-color: #17a2b8;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-print-all:hover {
  background-color: #138496;
}

.no-data {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
  font-style: italic;
}

table {
  width: 100%;
  border-collapse: collapse;
}

table th,
table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

table tbody tr:hover {
  background-color: #f8f9fa;
}

.btn-edit {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 0.5rem;
  font-size: 0.875rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-edit:hover {
  background-color: #1e7e34;
}

.btn-delete {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-delete:hover {
  background-color: #c82333;
}

.btn-print {
  background-color: #17a2b8;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 0.5rem;
  font-size: 0.875rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-print:hover {
  background-color: #138496;
}

/* Photo styles */
.photo-preview {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.preview-image {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #dee2e6;
}

.btn-remove-photo {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-remove-photo:hover {
  background-color: #c82333;
}

.student-photo {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid #dee2e6;
}

.no-photo {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 50%;
  font-size: 0.75rem;
  color: #6c757d;
  text-align: center;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 1rem;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-close:hover {
  color: #333;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  justify-content: center;
}

.btn-print-modal {
  background-color: #17a2b8;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-print-modal:hover {
  background-color: #138496;
}

.btn-cancel-modal {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-cancel-modal:hover {
  background-color: #545b62;
}

/* Student Card styles */
.student-card {
  width: 350px;
  height: 260px;
  border: 2px solid #007bff;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1rem;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card-header {
  text-align: center;
  border-bottom: 1px solid #007bff;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.card-header h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #007bff;
  font-weight: bold;
}

.card-header p {
  margin: 0.25rem 0 0 0;
  font-size: 0.9rem;
  color: #6c757d;
}

.card-body {
  display: flex;
  gap: 1rem;
  height: 120px;
}

.card-photo {
  flex-shrink: 0;
}

.card-photo img {
  width: 80px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #dee2e6;
}

.no-photo-card {
  width: 80px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  font-size: 0.75rem;
  color: #6c757d;
  text-align: center;
}

.card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-row {
  display: flex;
  align-items: center;
}

.info-row .label {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
  font-size: 0.85rem;
}

.info-row .value {
  color: #333;
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

.card-footer {
  text-align: center;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #dee2e6;
}

.card-footer p {
  margin: 0;
  font-size: 0.8rem;
  color: #6c757d;
}

/* Barcode styles */
.card-barcode {
  text-align: center;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #dee2e6;
}

.table-barcode {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 35px;
}

.table-barcode canvas {
  max-width: 100px;
}

.card-barcode canvas {
  max-width: 200px;
}

.no-barcode {
  font-size: 0.75rem;
  color: #6c757d;
  text-align: center;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border: 1px dashed #dee2e6;
  border-radius: 4px;
}

/* Print styles */
.print-all-cards {
  display: none;
}

@media print {
  /* ซ่อนทุกอย่างยกเว้นบัตร */
  body * {
    visibility: hidden;
  }

  .student-card, .student-card * {
    visibility: visible;
  }

  .print-all-cards {
    display: block !important;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    z-index: 9999;
  }

  .print-card {
    page-break-after: always;
    margin: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .print-card:last-child {
    page-break-after: avoid;
  }

  /* ซ่อน modal เมื่อพิมพ์ */
  .modal-overlay {
    display: none !important;
  }

  /* ปรับขนาดบัตรสำหรับการพิมพ์ */
  .student-card {
    width: 400px;
    height: 280px;
    margin: 0;
    box-shadow: none;
    border: 2px solid #000;
  }

  .card-barcode canvas {
    max-width: 250px;
  }
}

@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .form-buttons {
    flex-direction: column;
  }

  table {
    font-size: 0.875rem;
  }

  table th,
  table td {
    padding: 0.5rem;
  }

  .student-photo {
    width: 40px;
    height: 40px;
  }

  .no-photo {
    width: 40px;
    height: 40px;
    font-size: 0.625rem;
  }

  .preview-image {
    width: 120px;
    height: 120px;
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .btn-print-all {
    width: 100%;
  }

  .modal-content {
    width: 95%;
    padding: 1rem;
  }

  .student-card {
    width: 100%;
    max-width: 320px;
    height: auto;
    min-height: 200px;
  }

  .card-body {
    flex-direction: column;
    height: auto;
    gap: 0.5rem;
  }

  .card-photo {
    align-self: center;
  }

  .info-row .label {
    min-width: 70px;
    font-size: 0.8rem;
  }

  .info-row .value {
    font-size: 0.85rem;
  }

  .table-barcode canvas {
    max-width: 80px;
  }

  .card-barcode canvas {
    max-width: 150px;
  }
}
