.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app h1 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
  font-size: 2.5rem;
}

.form-container {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-container h2 {
  color: #495057;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.file-input {
  padding: 0.5rem !important;
  background-color: white;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.btn-primary {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-header {
  background: #007bff;
  color: white;
  margin: 0;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h2 {
  margin: 0;
  font-size: 1.25rem;
}

.btn-print-all {
  background-color: #17a2b8;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-print-all:hover {
  background-color: #138496;
}

.no-data {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
  font-style: italic;
}

table {
  width: 100%;
  border-collapse: collapse;
}

table th,
table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

table tbody tr:hover {
  background-color: #f8f9fa;
}

.btn-edit {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 0.5rem;
  font-size: 0.875rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-edit:hover {
  background-color: #1e7e34;
}

.btn-delete {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-delete:hover {
  background-color: #c82333;
}

.btn-print {
  background-color: #17a2b8;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 0.5rem;
  font-size: 0.875rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-print:hover {
  background-color: #138496;
}

/* Photo styles */
.photo-preview {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.preview-image {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #dee2e6;
}

.btn-remove-photo {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-remove-photo:hover {
  background-color: #c82333;
}

.student-photo {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid #dee2e6;
}

.no-photo {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 50%;
  font-size: 0.75rem;
  color: #6c757d;
  text-align: center;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 1rem;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-close:hover {
  color: #333;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  justify-content: center;
}

.btn-print-modal {
  background-color: #17a2b8;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-print-modal:hover {
  background-color: #138496;
}

.btn-cancel-modal {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.15s ease-in-out;
}

.btn-cancel-modal:hover {
  background-color: #545b62;
}

/* Student Card styles - มาตรฐานบัตรประจำตัว */
.student-card {
  width: 340px;  /* สัดส่วน 85.6mm */
  height: 214px; /* สัดส่วน 53.98mm */
  border: 1px solid #333;
  border-radius: 8px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 8px;
  margin: 0 auto;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
}

.card-header {
  text-align: center;
  background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
  color: white;
  margin: -8px -8px 8px -8px;
  padding: 6px 8px;
  border-radius: 8px 8px 0 0;
}

.card-header h2 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.card-header p {
  margin: 2px 0 0 0;
  font-size: 0.7rem;
  opacity: 0.9;
}

.card-body {
  display: flex;
  gap: 8px;
  height: 130px;
  align-items: flex-start;
}

.card-photo {
  flex-shrink: 0;
}

.card-photo img {
  width: 70px;
  height: 90px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.no-photo-card {
  width: 70px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 1px dashed #ccc;
  border-radius: 4px;
  font-size: 0.6rem;
  color: #6c757d;
  text-align: center;
}

.card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding-left: 4px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.info-row .label {
  font-weight: 600;
  color: #333;
  min-width: 60px;
  font-size: 0.7rem;
}

.info-row .value {
  color: #000;
  font-size: 0.75rem;
  margin-left: 4px;
  font-weight: 500;
}

.card-footer {
  position: absolute;
  bottom: 4px;
  right: 8px;
  text-align: right;
}

.card-footer p {
  margin: 0;
  font-size: 0.6rem;
  color: #666;
  font-style: italic;
}

/* Barcode styles */
.card-barcode {
  position: absolute;
  bottom: 4px;
  left: 8px;
  right: 80px;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 2px;
  padding: 2px;
}

.table-barcode {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 35px;
}

.table-barcode canvas {
  max-width: 100px;
}

.card-barcode canvas {
  max-width: 200px;
  height: auto;
  max-height: 25px;
}

.no-barcode {
  font-size: 0.75rem;
  color: #6c757d;
  text-align: center;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border: 1px dashed #dee2e6;
  border-radius: 4px;
}

/* Print styles */
.print-all-cards {
  display: none;
}

@media print {
  /* ซ่อนทุกอย่างยกเว้นบัตร */
  body * {
    visibility: hidden;
  }

  .student-card, .student-card * {
    visibility: visible;
  }

  .print-all-cards {
    display: block !important;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    z-index: 9999;
  }

  .print-card {
    page-break-after: always;
    margin: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .print-card:last-child {
    page-break-after: avoid;
  }

  /* ซ่อน modal เมื่อพิมพ์ */
  .modal-overlay {
    display: none !important;
  }

  /* ปรับขนาดบัตรสำหรับการพิมพ์ */
  .student-card {
    width: 85.6mm;  /* ขนาดมาตรฐานบัตร */
    height: 53.98mm;
    margin: 0;
    box-shadow: none;
    border: 1px solid #000;
    font-size: 8pt;
  }

  .card-header h2 {
    font-size: 10pt;
  }

  .card-header p {
    font-size: 7pt;
  }

  .info-row .label {
    font-size: 6pt;
  }

  .info-row .value {
    font-size: 7pt;
  }

  .card-footer p {
    font-size: 5pt;
  }

  .card-barcode canvas {
    max-width: 150px;
    max-height: 15px;
  }
}

@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .form-buttons {
    flex-direction: column;
  }

  table {
    font-size: 0.875rem;
  }

  table th,
  table td {
    padding: 0.5rem;
  }

  .student-photo {
    width: 40px;
    height: 40px;
  }

  .no-photo {
    width: 40px;
    height: 40px;
    font-size: 0.625rem;
  }

  .preview-image {
    width: 120px;
    height: 120px;
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .btn-print-all {
    width: 100%;
  }

  .modal-content {
    width: 95%;
    padding: 1rem;
  }

  .student-card {
    width: 100%;
    max-width: 320px;
    height: auto;
    min-height: 180px;
  }

  .card-body {
    flex-direction: column;
    height: auto;
    gap: 0.5rem;
  }

  .card-photo {
    align-self: center;
  }

  .card-photo img {
    width: 60px;
    height: 75px;
  }

  .no-photo-card {
    width: 60px;
    height: 75px;
  }

  .info-row .label {
    min-width: 70px;
    font-size: 0.65rem;
  }

  .info-row .value {
    font-size: 0.7rem;
  }

  .table-barcode canvas {
    max-width: 80px;
  }

  .card-barcode {
    position: static;
    margin-top: 0.5rem;
  }

  .card-barcode canvas {
    max-width: 200px;
  }

  .card-footer {
    position: static;
    text-align: center;
    margin-top: 0.5rem;
  }
}
