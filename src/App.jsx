import { useState, useEffect, useRef } from 'react'
import { v4 as uuidv4 } from 'uuid'
import JsBarcode from 'jsbarcode'
import './App.css'

// Component สำหรับสร้างบาร์โค้ด
function Barcode({ value, width = 2, height = 50, displayValue = true }) {
  const canvasRef = useRef(null)

  useEffect(() => {
    if (canvasRef.current && value) {
      try {
        JsBarcode(canvasRef.current, value, {
          format: "CODE128",
          width: width,
          height: height,
          displayValue: displayValue,
          fontSize: 12,
          textMargin: 2,
          margin: 5
        })
      } catch (error) {
        console.error('Error generating barcode:', error)
      }
    }
  }, [value, width, height, displayValue])

  if (!value) {
    return <div className="no-barcode">ไม่สามารถสร้างบาร์โค้ดได้</div>
  }

  return <canvas ref={canvasRef}></canvas>
}

function App() {
  const [students, setStudents] = useState([])
  const [formData, setFormData] = useState({
    id: '',
    studentId: '',
    name: '',
    room: '',
    grade: '',
    photo: null
  })
  const [isEditing, setIsEditing] = useState(false)
  const [showCardModal, setShowCardModal] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState(null)
  const [showAllCards, setShowAllCards] = useState(false)

  const grades = [
    'อนุบาล 1', 'อนุบาล 2', 'อนุบาล 3',
    'ป.1', 'ป.2', 'ป.3', 'ป.4', 'ป.5', 'ป.6'
  ]

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handlePhotoChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      // ตรวจสอบประเภทไฟล์
      if (!file.type.startsWith('image/')) {
        alert('กรุณาเลือกไฟล์รูปภาพเท่านั้น')
        return
      }

      // ตรวจสอบขนาดไฟล์ (จำกัดที่ 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('ขนาดไฟล์ต้องไม่เกิน 5MB')
        return
      }

      // แปลงเป็น base64 เพื่อเก็บใน state
      const reader = new FileReader()
      reader.onload = (event) => {
        setFormData(prev => ({
          ...prev,
          photo: event.target.result
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    if (!formData.studentId || !formData.name || !formData.room || !formData.grade) {
      alert('กรุณากรอกข้อมูลให้ครบถ้วน')
      return
    }

    if (isEditing) {
      setStudents(prev => prev.map(student =>
        student.id === formData.id ? formData : student
      ))
      setIsEditing(false)
    } else {
      const newStudent = {
        ...formData,
        id: uuidv4()
      }
      setStudents(prev => [...prev, newStudent])
    }

    setFormData({
      id: '',
      studentId: '',
      name: '',
      room: '',
      grade: '',
      photo: null
    })
  }

  const handleEdit = (student) => {
    setFormData(student)
    setIsEditing(true)
  }

  const handleDelete = (id) => {
    if (window.confirm('คุณต้องการลบข้อมูลนักเรียนนี้หรือไม่?')) {
      setStudents(prev => prev.filter(student => student.id !== id))
    }
  }

  const handleCancel = () => {
    setFormData({
      id: '',
      studentId: '',
      name: '',
      room: '',
      grade: '',
      photo: null
    })
    setIsEditing(false)
  }

  const handlePrintCard = (student) => {
    setSelectedStudent(student)
    setShowCardModal(true)
  }

  const handlePrintAllCards = () => {
    setShowAllCards(true)
    setTimeout(() => {
      window.print()
    }, 100)
  }

  const handlePrintSingleCard = () => {
    setShowCardModal(false)
    setShowAllCards(false)
    setTimeout(() => {
      window.print()
    }, 100)
  }

  const closeModal = () => {
    setShowCardModal(false)
    setShowAllCards(false)
    setSelectedStudent(null)
  }

  return (
    <div className="app">
      <h1>ระบบจัดการข้อมูลนักเรียน</h1>

      <div className="form-container">
        <h2>{isEditing ? 'แก้ไขข้อมูลนักเรียน' : 'เพิ่มข้อมูลนักเรียน'}</h2>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="studentId">รหัสนักเรียน:</label>
            <input
              type="text"
              id="studentId"
              name="studentId"
              value={formData.studentId}
              onChange={handleInputChange}
              placeholder="กรอกรหัสนักเรียน"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="name">ชื่อ-นามสกุล:</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="กรอกชื่อ-นามสกุล"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="room">ห้อง:</label>
            <input
              type="text"
              id="room"
              name="room"
              value={formData.room}
              onChange={handleInputChange}
              placeholder="เช่น ก, ข, ค, 1, 2, 3"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="grade">ระดับชั้น:</label>
            <select
              id="grade"
              name="grade"
              value={formData.grade}
              onChange={handleInputChange}
              required
            >
              <option value="">เลือกระดับชั้น</option>
              {grades.map(grade => (
                <option key={grade} value={grade}>{grade}</option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="photo">รูปภาพนักเรียน:</label>
            <input
              type="file"
              id="photo"
              name="photo"
              accept="image/*"
              onChange={handlePhotoChange}
              className="file-input"
            />
            {formData.photo && (
              <div className="photo-preview">
                <img
                  src={formData.photo}
                  alt="ตัวอย่างรูปภาพ"
                  className="preview-image"
                />
                <button
                  type="button"
                  onClick={() => setFormData(prev => ({...prev, photo: null}))}
                  className="btn-remove-photo"
                >
                  ลบรูปภาพ
                </button>
              </div>
            )}
          </div>

          <div className="form-buttons">
            <button type="submit" className="btn-primary">
              {isEditing ? 'บันทึกการแก้ไข' : 'เพิ่มนักเรียน'}
            </button>
            {isEditing && (
              <button type="button" onClick={handleCancel} className="btn-secondary">
                ยกเลิก
              </button>
            )}
          </div>
        </form>
      </div>

      <div className="table-container">
        <div className="table-header">
          <h2>รายชื่อนักเรียน ({students.length} คน)</h2>
          {students.length > 0 && (
            <button
              onClick={handlePrintAllCards}
              className="btn-print-all"
            >
              พิมพ์บัตรทั้งหมด
            </button>
          )}
        </div>
        {students.length === 0 ? (
          <p className="no-data">ยังไม่มีข้อมูลนักเรียน</p>
        ) : (
          <table>
            <thead>
              <tr>
                <th>รูปภาพ</th>
                <th>รหัสนักเรียน</th>
                <th>บาร์โค้ด</th>
                <th>ชื่อ-นามสกุล</th>
                <th>ห้อง</th>
                <th>ระดับชั้น</th>
                <th>การจัดการ</th>
              </tr>
            </thead>
            <tbody>
              {students.map(student => (
                <tr key={student.id}>
                  <td>
                    {student.photo ? (
                      <img
                        src={student.photo}
                        alt={`รูปภาพของ ${student.name}`}
                        className="student-photo"
                      />
                    ) : (
                      <div className="no-photo">ไม่มีรูปภาพ</div>
                    )}
                  </td>
                  <td>{student.studentId}</td>
                  <td>
                    <div className="table-barcode">
                      <Barcode
                        value={student.studentId}
                        width={1}
                        height={25}
                        displayValue={false}
                      />
                    </div>
                  </td>
                  <td>{student.name}</td>
                  <td>{student.room}</td>
                  <td>{student.grade}</td>
                  <td>
                    <button
                      onClick={() => handleEdit(student)}
                      className="btn-edit"
                    >
                      แก้ไข
                    </button>
                    <button
                      onClick={() => handleDelete(student.id)}
                      className="btn-delete"
                    >
                      ลบ
                    </button>
                    <button
                      onClick={() => handlePrintCard(student)}
                      className="btn-print"
                    >
                      พิมพ์บัตร
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* Modal สำหรับแสดงบัตรนักเรียน */}
      {showCardModal && selectedStudent && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>บัตรนักเรียน</h3>
              <button onClick={closeModal} className="btn-close">×</button>
            </div>
            <div className="student-card">
              <div className="card-header">
                <h2>โรงเรียนตัวอย่าง</h2>
                <p>บัตรประจำตัวนักเรียน</p>
              </div>
              <div className="card-body">
                <div className="card-photo">
                  {selectedStudent.photo ? (
                    <img src={selectedStudent.photo} alt={selectedStudent.name} />
                  ) : (
                    <div className="no-photo-card">ไม่มีรูปภาพ</div>
                  )}
                </div>
                <div className="card-info">
                  <div className="info-row">
                    <span className="label">รหัสนักเรียน:</span>
                    <span className="value">{selectedStudent.studentId}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">ชื่อ-นามสกุล:</span>
                    <span className="value">{selectedStudent.name}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">ห้อง:</span>
                    <span className="value">{selectedStudent.room}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">ระดับชั้น:</span>
                    <span className="value">{selectedStudent.grade}</span>
                  </div>
                </div>
              </div>
              <div className="card-barcode">
                <Barcode
                  value={selectedStudent.studentId}
                  width={1.5}
                  height={30}
                  displayValue={false}
                />
              </div>
              </div>
              <div className="card-footer">
                <p>ปีการศึกษา 2567</p>
              </div>
            </div>
            <div className="modal-actions">
              <button onClick={handlePrintSingleCard} className="btn-print-modal">
                พิมพ์บัตร
              </button>
              <button onClick={closeModal} className="btn-cancel-modal">
                ปิด
              </button>
            </div>
          </div>
        </div>
      )}

      {/* หน้าสำหรับพิมพ์บัตรทั้งหมด */}
      {showAllCards && (
        <div className="print-all-cards">
          {students.map(student => (
            <div key={student.id} className="student-card print-card">
              <div className="card-header">
                <h2>โรงเรียนตัวอย่าง</h2>
                <p>บัตรประจำตัวนักเรียน</p>
              </div>
              <div className="card-body">
                <div className="card-photo">
                  {student.photo ? (
                    <img src={student.photo} alt={student.name} />
                  ) : (
                    <div className="no-photo-card">ไม่มีรูปภาพ</div>
                  )}
                </div>
                <div className="card-info">
                  <div className="info-row">
                    <span className="label">รหัสนักเรียน:</span>
                    <span className="value">{student.studentId}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">ชื่อ-นามสกุล:</span>
                    <span className="value">{student.name}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">ห้อง:</span>
                    <span className="value">{student.room}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">ระดับชั้น:</span>
                    <span className="value">{student.grade}</span>
                  </div>
                </div>
              </div>
              <div className="card-barcode">
                <Barcode
                  value={student.studentId}
                  width={1.5}
                  height={30}
                  displayValue={false}
                />
              </div>
              </div>
              <div className="card-footer">
                <p>ปีการศึกษา 2567</p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default App
