import { useState } from 'react'
import { v4 as uuidv4 } from 'uuid'
import './App.css'

function App() {
  const [students, setStudents] = useState([])
  const [formData, setFormData] = useState({
    id: '',
    studentId: '',
    name: '',
    room: '',
    grade: ''
  })
  const [isEditing, setIsEditing] = useState(false)

  const grades = [
    'อนุบาล 1', 'อนุบาล 2', 'อนุบาล 3',
    'ป.1', 'ป.2', 'ป.3', 'ป.4', 'ป.5', 'ป.6'
  ]

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    if (!formData.studentId || !formData.name || !formData.room || !formData.grade) {
      alert('กรุณากรอกข้อมูลให้ครบถ้วน')
      return
    }

    if (isEditing) {
      setStudents(prev => prev.map(student =>
        student.id === formData.id ? formData : student
      ))
      setIsEditing(false)
    } else {
      const newStudent = {
        ...formData,
        id: uuidv4()
      }
      setStudents(prev => [...prev, newStudent])
    }

    setFormData({
      id: '',
      studentId: '',
      name: '',
      room: '',
      grade: ''
    })
  }

  const handleEdit = (student) => {
    setFormData(student)
    setIsEditing(true)
  }

  const handleDelete = (id) => {
    if (window.confirm('คุณต้องการลบข้อมูลนักเรียนนี้หรือไม่?')) {
      setStudents(prev => prev.filter(student => student.id !== id))
    }
  }

  const handleCancel = () => {
    setFormData({
      id: '',
      studentId: '',
      name: '',
      room: '',
      grade: ''
    })
    setIsEditing(false)
  }

  return (
    <div className="app">
      <h1>ระบบจัดการข้อมูลนักเรียน</h1>

      <div className="form-container">
        <h2>{isEditing ? 'แก้ไขข้อมูลนักเรียน' : 'เพิ่มข้อมูลนักเรียน'}</h2>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="studentId">รหัสนักเรียน:</label>
            <input
              type="text"
              id="studentId"
              name="studentId"
              value={formData.studentId}
              onChange={handleInputChange}
              placeholder="กรอกรหัสนักเรียน"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="name">ชื่อ-นามสกุล:</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="กรอกชื่อ-นามสกุล"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="room">ห้อง:</label>
            <input
              type="text"
              id="room"
              name="room"
              value={formData.room}
              onChange={handleInputChange}
              placeholder="เช่น ก, ข, ค, 1, 2, 3"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="grade">ระดับชั้น:</label>
            <select
              id="grade"
              name="grade"
              value={formData.grade}
              onChange={handleInputChange}
              required
            >
              <option value="">เลือกระดับชั้น</option>
              {grades.map(grade => (
                <option key={grade} value={grade}>{grade}</option>
              ))}
            </select>
          </div>

          <div className="form-buttons">
            <button type="submit" className="btn-primary">
              {isEditing ? 'บันทึกการแก้ไข' : 'เพิ่มนักเรียน'}
            </button>
            {isEditing && (
              <button type="button" onClick={handleCancel} className="btn-secondary">
                ยกเลิก
              </button>
            )}
          </div>
        </form>
      </div>

      <div className="table-container">
        <h2>รายชื่อนักเรียน ({students.length} คน)</h2>
        {students.length === 0 ? (
          <p className="no-data">ยังไม่มีข้อมูลนักเรียน</p>
        ) : (
          <table>
            <thead>
              <tr>
                <th>รหัสนักเรียน</th>
                <th>ชื่อ-นามสกุล</th>
                <th>ห้อง</th>
                <th>ระดับชั้น</th>
                <th>การจัดการ</th>
              </tr>
            </thead>
            <tbody>
              {students.map(student => (
                <tr key={student.id}>
                  <td>{student.studentId}</td>
                  <td>{student.name}</td>
                  <td>{student.room}</td>
                  <td>{student.grade}</td>
                  <td>
                    <button
                      onClick={() => handleEdit(student)}
                      className="btn-edit"
                    >
                      แก้ไข
                    </button>
                    <button
                      onClick={() => handleDelete(student.id)}
                      className="btn-delete"
                    >
                      ลบ
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  )
}

export default App
