language: node_js
sudo: required
before_install:
  - sudo add-apt-repository -y ppa:ubuntu-toolchain-r/test
  - sudo apt-get update
  - npm install -g gulp
install:
  - sudo apt-get install libcairo2-dev libjpeg8-dev libpango1.0-dev libgif-dev build-essential g++-4.8
  - sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-4.8 50
  - npm install
after_success: npm run coveralls
node_js:
  - "node"
  - "14"
  - "12"
  - "10"
