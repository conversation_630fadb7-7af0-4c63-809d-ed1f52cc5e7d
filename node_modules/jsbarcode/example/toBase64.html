<!DOCTYPE HTML>
<html lang="en-US">
<head>
	<meta charset="UTF-8">
	<title></title>
	<script src="../dist/JsBarcode.all.js"></script>
  <script>
    function textToBase64Barcode(text){
      var canvas = document.createElement("canvas");
      JsBarcode(canvas, text, {format: "CODE39"});
      return canvas.toDataURL("image/png");
    }
  </script>
</head>
<body>
  <a id="hello">Click Here</a>
  <script>
    document.querySelector("#hello").href = textToBase64Barcode("HELLO");
  </script>
</body>
</html>
